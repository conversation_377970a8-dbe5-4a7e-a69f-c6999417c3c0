.layui-flow-more{ clear: both; margin-top: 20px; }
#Images li{ width:19%; margin:0.5% 0.5%; float: left; overflow:hidden;}
#Images li img{ width:100%; }
#Images li .operate{ display: block; height: 40px; width:100%; background:#f4f5f9; }
#Images li .operate .check{ float:left; margin-left:11px; height:18px; padding:11px 0; }
#Images li .operate .img_del{ float:right; margin:7px 11px 0 0; font-size: 22px; cursor:pointer; }
#Images li .operate .img_del:hover{ color:#f00; }

/*适配*/
@media screen and (max-width:1050px){
	/*用户信息*/
	#Images li{ width:24%;}
}
@media screen and (max-width: 750px){
	/*用户信息*/
	#Images li{ width:49%;}
}
@media screen and (max-width:432px){
	/*用户信息*/
	#Images li{ width:99%;}
}