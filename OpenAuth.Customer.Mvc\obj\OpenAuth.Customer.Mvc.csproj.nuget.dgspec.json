{"format": 1, "restore": {"D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\OpenAuth.Customer.Mvc.csproj": {}}, "projects": {"D:\\代码\\Web_OV2\\Infrastructure\\Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\代码\\Web_OV2\\Infrastructure\\Infrastructure.csproj", "projectName": "Infrastructure", "projectPath": "D:\\代码\\Web_OV2\\Infrastructure\\Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\代码\\Web_OV2\\Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\hih\\DevExpress\\Offline Packages", "D:\\tools\\DevExpress 24.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 20.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\hih\\DevExpress\\System\\Components\\Packages": {}, "D:\\tools\\DevExpress 24.2\\Components\\System\\Components\\Packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"AutoMapper": {"target": "Package", "version": "[9.0.0, )"}, "EnyimMemcachedCore": {"target": "Package", "version": "[2.5.3, )"}, "Microsoft.AspNetCore.Http": {"target": "Package", "version": "[2.2.2, )"}, "Microsoft.Extensions.Caching.Memory": {"target": "Package", "version": "[5.0.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[5.0.0, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[16.5.0, )"}, "NUnit": {"target": "Package", "version": "[3.13.1, )"}, "NUnit3TestAdapter": {"target": "Package", "version": "[3.17.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "SixLabors.ImageSharp": {"target": "Package", "version": "[1.0.2, )"}, "SqlSugarCore": {"target": "Package", "version": "[*********, )"}, "StackExchange.Redis": {"target": "Package", "version": "[2.2.4, )"}, "log4net": {"target": "Package", "version": "[2.0.12, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\代码\\Web_OV2\\OpenAuth.App\\OpenAuth.App.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\代码\\Web_OV2\\OpenAuth.App\\OpenAuth.App.csproj", "projectName": "OpenAuth.App", "projectPath": "D:\\代码\\Web_OV2\\OpenAuth.App\\OpenAuth.App.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\代码\\Web_OV2\\OpenAuth.App\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\hih\\DevExpress\\Offline Packages", "D:\\tools\\DevExpress 24.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 20.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\hih\\DevExpress\\System\\Components\\Packages": {}, "D:\\tools\\DevExpress 24.2\\Components\\System\\Components\\Packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\代码\\Web_OV2\\Infrastructure\\Infrastructure.csproj": {"projectPath": "D:\\代码\\Web_OV2\\Infrastructure\\Infrastructure.csproj"}, "D:\\代码\\Web_OV2\\OpenAuth.Repository\\OpenAuth.Repository.csproj": {"projectPath": "D:\\代码\\Web_OV2\\OpenAuth.Repository\\OpenAuth.Repository.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Aliyun.OSS.SDK.NetCore": {"target": "Package", "version": "[2.14.1, )"}, "Autofac": {"target": "Package", "version": "[5.2.0, )"}, "Autofac.Extensions.DependencyInjection": {"target": "Package", "version": "[6.0.0, )"}, "Autofac.Extras.Quartz": {"target": "Package", "version": "[5.1.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[3.1.2, )"}, "Microsoft.AspNetCore.Authentication.OpenIdConnect": {"target": "Package", "version": "[3.1.2, )"}, "Microsoft.Extensions.DependencyModel": {"target": "Package", "version": "[8.0.2, )"}, "Microsoft.Extensions.Http": {"target": "Package", "version": "[5.0.0, )"}, "Microsoft.Extensions.Logging.Log4Net.AspNetCore": {"target": "Package", "version": "[3.1.0, )"}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"target": "Package", "version": "[5.0.0, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[16.5.0, )"}, "Microsoft.Windows.Compatibility": {"target": "Package", "version": "[5.0.2, )"}, "Moq": {"target": "Package", "version": "[4.13.1, )"}, "NPOI": {"target": "Package", "version": "[2.7.2, )"}, "NUnit": {"target": "Package", "version": "[3.13.1, )"}, "NUnit3TestAdapter": {"target": "Package", "version": "[3.17.0, )"}, "Quartz": {"target": "Package", "version": "[3.0.7, )"}, "aliyun-net-sdk-sts": {"target": "Package", "version": "[3.1.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\OpenAuth.Customer.Mvc.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\OpenAuth.Customer.Mvc.csproj", "projectName": "OpenAuth.Customer.Mvc", "projectPath": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\OpenAuth.Customer.Mvc.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\代码\\Web_OV2\\OpenAuth.Customer.Mvc\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\hih\\DevExpress\\Offline Packages", "D:\\tools\\DevExpress 24.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 20.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\hih\\DevExpress\\System\\Components\\Packages": {}, "D:\\tools\\DevExpress 24.2\\Components\\System\\Components\\Packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\代码\\Web_OV2\\Infrastructure\\Infrastructure.csproj": {"projectPath": "D:\\代码\\Web_OV2\\Infrastructure\\Infrastructure.csproj"}, "D:\\代码\\Web_OV2\\OpenAuth.App\\OpenAuth.App.csproj": {"projectPath": "D:\\代码\\Web_OV2\\OpenAuth.App\\OpenAuth.App.csproj"}, "D:\\代码\\Web_OV2\\OpenAuth.Repository\\OpenAuth.Repository.csproj": {"projectPath": "D:\\代码\\Web_OV2\\OpenAuth.Repository\\OpenAuth.Repository.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Autofac.Extensions.DependencyInjection": {"target": "Package", "version": "[6.0.0, )"}, "IdentityServer4.AccessTokenValidation": {"target": "Package", "version": "[3.0.1, )"}, "Microsoft.AspNet.Web.Optimization": {"target": "Package", "version": "[1.1.3, )"}, "Microsoft.AspNet.Web.Optimization.zh-Hans": {"target": "Package", "version": "[1.1.3, )"}, "Microsoft.Extensions.Logging.Log4Net.AspNetCore": {"target": "Package", "version": "[3.1.0, )"}, "Microsoft.VisualStudio.Web.CodeGeneration.Design": {"target": "Package", "version": "[3.1.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}, "D:\\代码\\Web_OV2\\OpenAuth.Repository\\OpenAuth.Repository.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\代码\\Web_OV2\\OpenAuth.Repository\\OpenAuth.Repository.csproj", "projectName": "OpenAuth.Repository", "projectPath": "D:\\代码\\Web_OV2\\OpenAuth.Repository\\OpenAuth.Repository.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\代码\\Web_OV2\\OpenAuth.Repository\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\hih\\DevExpress\\Offline Packages", "D:\\tools\\DevExpress 24.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 20.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 24.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\hih\\DevExpress\\System\\Components\\Packages": {}, "D:\\tools\\DevExpress 24.2\\Components\\System\\Components\\Packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\代码\\Web_OV2\\Infrastructure\\Infrastructure.csproj": {"projectPath": "D:\\代码\\Web_OV2\\Infrastructure\\Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"AngleSharp": {"target": "Package", "version": "[1.1.2, )"}, "Autofac": {"target": "Package", "version": "[5.2.0, )"}, "Autofac.Extensions.DependencyInjection": {"target": "Package", "version": "[6.0.0, )"}, "Microsoft.EntityFrameworkCore.Relational": {"target": "Package", "version": "[6.0.27, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[6.0.27, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[16.5.0, )"}, "Moq": {"target": "Package", "version": "[4.13.1, )"}, "MySql.Data": {"target": "Package", "version": "[8.0.13, )"}, "NUnit": {"target": "Package", "version": "[3.13.1, )"}, "NUnit3TestAdapter": {"target": "Package", "version": "[3.17.0, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[6.0.22, )"}, "Oracle.EntityFrameworkCore": {"target": "Package", "version": "[6.21.130, )"}, "Oracle.ManagedDataAccess.Core": {"target": "Package", "version": "[3.21.130, )"}, "Pomelo.EntityFrameworkCore.MySql": {"target": "Package", "version": "[6.0.2, )"}, "SqlSugarCore": {"target": "Package", "version": "[*********, )"}, "Z.EntityFramework.Plus.EFCore": {"target": "Package", "version": "[6.102.1.1, )"}, "iTextSharp": {"target": "Package", "version": "[5.5.13.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.204\\RuntimeIdentifierGraph.json"}}}}}