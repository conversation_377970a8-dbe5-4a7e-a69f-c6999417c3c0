﻿var appModule = angular.module('myApp', ['ui.bootstrap']);

(function () {

    //封装一个SelectModal
    appModule.controller('compoment.selectModalCtrl', [
        '$scope', '$uibModalInstance', 'selectModalOptions',
        function ($scope, $uibModalInstance, selectModalOptions) {
            var vm = this;
            vm.selectItem = selectModalOptions.retProperty;
            var gridApi;
            class RowCountStatusBarComponent {
                init(params) {
                    this.params = params;
                    this.eGui = document.createElement('div');
                    this.eGui.className = 'ag-status-name-value';
                    /*      this.eGui.style =" flex: 0 0 auto;";*/
                    this.updateRowCount();

                    // 监听数据变化事件
                    this.params.api.addEventListener('firstDataRendered', () => {
                        this.updateRowCount();
                    });
                    this.params.api.addEventListener('modelUpdated', () => {
                        this.updateRowCount();
                    });
                }

                getGui() {
                    return this.eGui;
                }

                refresh(params) {
                    this.updateRowCount();
                    return true;
                }

                updateRowCount() {
                    const totalRows = this.params.api.getModel().getRowCount();
                    this.eGui.innerHTML = `<span style="font-weight: bold; border-right: 1px solid #e0e0e0; padding: 0 10px;">Total Rows: ${totalRows}</span>`;
                }
            }
            var gridOptions = {
                columnDefs: selectModalOptions.columns,
                rowData: [],
                rowSelection: selectModalOptions.isCheckSingle == true ? "single" :"multiple",  // 'single' 表示单选模式multiple
                onGridReady: function (params) {
                    gridApi = params.api;
                },
                statusBar: {
                    statusPanels: [
                        {
                            statusPanel: RowCountStatusBarComponent,
                            align: 'left',
                        },
                    ],
                },
            };
            vm.specialFilter = function () {
                var param = {
                    ...selectModalOptions.filterParams,
                    filterText : vm.filterText,
                };
                return param;
            }

            

            var ajaxData = function () {
                $.ajax({
                    url: selectModalOptions.url,
                    type: 'post',
                    data: (vm.specialFilter()), // 发送包含参数的 JSON 对象
                    success: function (response) {
                        gridApi.setGridOption("rowData", response.data)
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        if (jqXHR.responseText) {
                            myAlert.message.error(jqXHR.responseText);
                        }
                    }
                });
            }

            vm.gridReflesh = function () {
                ajaxData();
            }
            // 确认选择
            vm.confirm = function () {
                const selectedRows = gridApi.getSelectedRows();
                if (selectedRows.length == 0) {
                    myAlert.message.warn('please select one item');
                    return;
                }
                selectModalOptions.callback(selectedRows);
                $uibModalInstance.close(selectedRows);
            };

            // 取消操作
            vm.cancel = function () {
                $uibModalInstance.dismiss();
            };
            vm.init = function () {
                vm.title = selectModalOptions.title;
                new agGrid.Grid(document.getElementById('agGrid'), gridOptions);
                ajaxData();
            };
        }
    ]);

    appModule.factory('selectModal', [
        '$uibModal',
        function ($uibModal) {

            function open(selectModalOptions) {
                $uibModal.open({
                    templateUrl: '/Component/SelectModal',
                    controller: 'compoment.selectModalCtrl as vm',
                    backdrop: 'static',
                    size: selectModalOptions.size || 'lg',
                    resolve: {
                        selectModalOptions: function () {
                            return selectModalOptions;
                        }
                    }
                });
            }

            return {
                open: open
            };
        }
    ]);

})();
//appModule.directive('selectPicker', function ($timeout) {
//    return {
//        restrict: 'A',
//        link: function (scope, element) {
//            // 确保 selectpicker 被正确初始化和刷新
//            scope.$watch(function () {
//                return $(element).find('option').length;
//            }, function (newVal) {
//                if (newVal > 0) {
//                    $timeout(function () {
//                        $(element).selectpicker('refresh');
//                    });
//                }
//            });
//            scope.$watch(attrs.ngDisabled, function (newVal) {
//                $timeout(function () {
//                    $(element).selectpicker('refresh');
//                });
//            });
//        }
//    };
//});
appModule.directive('selectPicker', ['$timeout', function ($timeout) {
    return {
        restrict: 'A',
        link: function (scope, element, attrs) {
            // 监控选项数量变化
            scope.$watch(function () {
                return $(element).find('option').length;
            }, function (newVal) {
                if (newVal > 0) {
                    var firstOptionValue = $(element).find('option').eq(0).val();
                    if (scope[attrs.ngModel] !== firstOptionValue) {
                        scope[attrs.ngModel] = firstOptionValue;
                    }
                    $timeout(function () {
                        $(element).selectpicker('refresh');
                    });
                }
            });

            // 监控 ng-disabled 变化
            scope.$watch(attrs.ngDisabled, function (newVal) {
                $timeout(function () {
                    $(element).selectpicker('refresh');
                });
            });

            // 监控 ng-model 变化（如果需要的话）
            scope.$watch(attrs.ngModel, function (newVal) {
                $timeout(function () {
                    $(element).selectpicker('refresh');
                });
            });
        }
    };
}]);
(function () {
    appModule.directive('busyIf', [
        function () {
            return {
                restrict: 'A',
                scope: {
                    busyIf: "="
                },
                link: function (scope, element, attrs) {
                    scope.$watch('busyIf', function () {
                        if (scope.busyIf) {
                            abp.ui.setBusy($(element));
                        } else {
                            abp.ui.clearBusy($(element));
                        }
                    });
                }
            };
        }
    ]);
})();
(function () {
    appModule.directive('buttonBusy', function () {
        return {
            restrict: 'A',
            scope: {
                buttonBusy: '='
            },
            link: function ($scope, element, attrs) {

                var disabledBefore = false;
                var $button = $(element);
                var $buttonInnerSpan = $button.find('span');
                var buttonOriginalText = null;

                var $icon = $button.find('i');
                var iconOriginalClasses = null;

                $scope.$watch('buttonBusy', function () {
                    if ($scope.buttonBusy) {
                        //disable button
                        $button.attr('disabled', 'disabled');
                        //change icon
                        if ($icon.length) {
                            iconOriginalClasses = $icon.attr('class');
                            $icon.removeClass();
                            $icon.addClass('fa fa-spin fa-spinner');
                        }
                        //change text
                        if (attrs.busyText && $buttonInnerSpan.length) {
                            buttonOriginalText = $buttonInnerSpan.html();
                            $buttonInnerSpan.html(attrs.busyText);
                        }

                        disabledBefore = true;
                    } else {
                        if (!disabledBefore) {
                            return;
                        }

                        //enable button
                        $button.removeAttr('disabled');
                        //restore icon
                        if ($icon.length && iconOriginalClasses) {
                            $icon.removeClass();
                            $icon.addClass(iconOriginalClasses);
                        }
                        //restore text
                        if ($buttonInnerSpan.length && buttonOriginalText) {
                            $buttonInnerSpan.html(buttonOriginalText);
                        }
                    }
                });
            }
        };
    });
})();

appModule.directive('modalInert', function ($timeout) {
    return {
        restrict: 'A',
        link: function (scope, element, attrs) {
            scope.$on('modalOpen', function () {
                $timeout(function () {
                    element.attr('inert', ''); // 禁用交互
                });
            });

            scope.$on('modalClose', function () {
                $timeout(function () {
                    element.removeAttr('inert'); // 恢复交互
                });
            });
        }
    };
});
appModule.directive('modalMovable', ['$document',
    function ($document) {
        return {
            restrict: 'AC',
            link: function (scope, iElement, iAttrs) {
                var startX = 0,
                    startY = 0,
                    x = 0,
                    y = 0;
                setTimeout(function () {
                    $(".modal-header").css({ cursor: "move" });
                }, 1000);
                var dialogWrapper = iElement.parent();
                var rect = null;
                var headerHeight = 0;
                dialogWrapper.css({
                    position: "relative"
                });

                dialogWrapper.on('mousedown', function (event) {
                    if (!(event.target 
                        && event.target.className
                        && event.target.className == "modal-title")) {
                        return;
                    }
                    if (rect == null) {
                        rect = dialogWrapper[0].getBoundingClientRect();
                        headerHeight = $(".modal-header")[0].offsetHeight;
                    }
                    // Prevent default dragging of selected content
                    event.preventDefault();
                    startX = event.pageX - x;
                    startY = event.pageY - y;
                    $document.on('mousemove', mousemove);
                    $document.on('mouseup', mouseup);
                });

                function mousemove(event) {
                    y = event.pageY - startY;
                    x = event.pageX - startX;

                    if (rect.left + x < -rect.width / 2) {
                        x = -rect.left - rect.width / 2;
                    } else if (rect.left + x > window.innerWidth - rect.width / 2) {
                        x = window.innerWidth - rect.width / 2 - rect.left;
                    }
                    if (rect.top + y < 0) {
                        y = -rect.top;
                    } else if (rect.top + y > window.innerHeight - headerHeight) {
                        y = window.innerHeight - headerHeight - rect.top;;
                    }

                    dialogWrapper.css({
                        top: y + 'px',
                        left: x + 'px'
                    });
                }

                function mouseup() {
                    $document.unbind('mousemove', mousemove);
                    $document.unbind('mouseup', mouseup);
                }
            }
        };
    }
]);
appModule.directive('stringToNumber', function () {
    return {
        require: 'ngModel',
        link: function (scope, element, attrs, ngModel) {
            // 将视图值（字符串）转换为模型值（数字）
            ngModel.$parsers.push(function (value) {
                if (value === '') { // 如果值为空，返回null或undefined
                    return null; // 或者 return undefined;
                }
                return parseFloat(value, 10); // 将字符串转换为浮点数
            });

            // 将模型值（数字）转换为视图值（字符串）
            ngModel.$formatters.push(function (value) {
                if (value === null || value === undefined) { // 如果值为null或undefined，返回空字符串
                    return '';
                }
                return '' + value; // 将数字转换为字符串
            });
        }
    };
});