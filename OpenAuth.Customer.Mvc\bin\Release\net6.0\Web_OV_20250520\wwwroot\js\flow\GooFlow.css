v\:group,v\:rect,v\:imagedata,v\:oval,v\:line,v\:polyline,v\:stroke,v\:textbox { display:inline-block;background:transparent }
::-ms-clear,::-ms-reveal{display:none;}
/*总体样式*/
.GooFlow{
	background:#f1f1f1;border:#ddd 1px solid;position:relative;
	-moz-user-select:none;-webkit-user-select:none;border-radius:4px;color:#333
}
.GooFlow,.GooFlow *{
	-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box; font-size:14px;line-height:1.42857143;
	font-family: "Microsoft Yahei", "Helvetica Neue", Helvetica, Hiragino Sans GB, WenQuanYi Micro Hei, Arial, sans-serif;
}
.GooFlow:before,.GooFlow:after,.GooFlow *:before,.GooFlow *:after{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}
.GooFlow i{
	display:block;width:18px;height:18px;overflow:hidden;font-size:18px;color:#777;text-align:center;margin:0 auto;
	filter:Alpha(Opacity=70);-moz-opacity:0.7;opacity: 0.7;text-shadow:0.5px 0 #fff,0 0.5px #fff;line-height:1;
}
.GooFlow a.a_disabled,.GooFlow a.a_disabled:hover {border:0 !important;padding:4px 6px;background: transparent !important; cursor: not-allowed !important;}
.GooFlow a.a_disabled i:before{color:#ccc !important;}
/*绘图区的样式*/
.GooFlow_work{position:absolute;top:3px;right:3px;bottom:3px;left:3px;overflow:auto;background-color:#fff;}
.GooFlow_work .GooFlow_work_inner{
    /*background: -webkit-linear-gradient(top, transparent 11px, #e3e3e3 12px),-webkit-linear-gradient(left, transparent 11px, #e3e3e3 12px);*/
    /*background: -moz-linear-gradient(top, transparent 11px, #e3e3e3 12px),-moz-linear-gradient(left, transparent 11px, #e3e3e3 12px);*/
    /*background: -o-linear-gradient(top, transparent 11px, #e3e3e3 12px),-o-linear-gradient(left, transparent 11px, #e3e3e3 12px);*/
    /*background: -ms-linear-gradient(top, transparent 11px, #e3e3e3 12px),-ms-linear-gradient(left, transparent 11px, #e3e3e3 12px);*/
    /*background: linear-gradient(top, transparent 11px, #e3e3e3 12px),linear-gradient(left, transparent 11px, #e3e3e3 12px);*/
    /*-webkit-background-size: 12px 12px; -moz-background-size: 12px 12px; background-size: 12px 12px;*/
    position:relative;overflow:hidden; background-image:url(data:image/gif;base64,R0lGODlhDAAMAJEAAOrq6v////Hx8QAAACH5BAAHAP8ALAAAAAAMAAwAAAIWjI8hycvonomSPtvkwYlDPQniSApAAQA7);
}
/*与矢量线有关的样式*/
.GooFlow_work text{color:#fff;font-size:14px;line-height:1.42857143;
    font-family: "Microsoft Yahei", "Helvetica Neue", Helvetica, Hiragino Sans GB, WenQuanYi Micro Hei, Arial, sans-serif;}

/*顶部栏的样式*/
.GooFlow_head{clear:both;height:28px;border-bottom:#00B4E1 2px solid;margin-left:-1px}
.GooFlow_head label{
	font-weight:bold;display:block;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;height:27px;padding:3px;width:176px;
	background:#00B4E1;float:left;color:#fff;border-radius:3px 0 0 0;overflow:hidden;margin:-1px 4px 0 0;text-align:center;
}
.GooFlow_head_btn{display:block;border:0;height:26px;width:30px;cursor:default;padding:4px 6px;margin:0 2px;float:left;outline:none;blr:expression(this.onFocus=this.blur());text-decoration:none;cursor:pointer}
.GooFlow_head_btn i{display:inline-block;overflow:hidden;width:18px;height:18px;border:0;font-size:16px;line-height:18px;}
.GooFlow_head_btn:hover{background:#fff;padding:4px 5px;border-left:#ddd 1px solid;border-right:#ddd 1px solid}

/*左侧绘图工具栏的样式*/
.GooFlow_tool{float:left;clear:left;border-right:#ddd 1px solid;margin-left:-1px;}
.GooFlow_tool_div{width:34px;padding:4px 0;overflow:hidden;margin-right:-1px;}
.GooFlow_tool span{height:0;overflow:hidden;border-top:#ddd 1px solid;border-bottom:#fff 1px solid;margin:0 2px;clear:both;display:block;}
.GooFlow_tool_btn{display:block;border:0;height:34px;width:34px;cursor:default;padding:8px;outline:none;blr:expression(this.onFocus=this.blur());color:#777;text-decoration:none;cursor:pointer}
.GooFlow_tool_btn i{display:block;overflow:hidden;width:18px;height:18px;border:0}
.GooFlow_tool_btn:hover{border:#ddd 1px solid;background:#fff;padding:7px}
.GooFlow_tool_btndown{
	cursor:default;outline:none;blr:expression(this.onFocus=this.blur());height:34px;width:36px;margin:0 -1px;
	padding:8px 9px;background:#00B4E1;/*#FFBF00*/display:block;text-decoration:none;filter:Alpha(Opacity=70);-moz-opacity:0.7;opacity: 0.7;
}
.GooFlow_tool_btndown i{display:block;overflow:hidden;width:18px;height:18px;color:#000;filter:Alpha(Opacity=37);-moz-opacity:0.37;opacity: 0.37;position:relative}

/*工作区扩展边栏样式*/
.Gooflow_extend_right{position:absolute;z-index:10002;top:0;right:0;height:100%;width:14px;cursor:e-resize;filter:Alpha(Opacity=20);-moz-opacity:0.2;opacity: 0.2}
.Gooflow_extend_bottom{position:absolute;z-index:10002;bottom:0;left:0;width:100%;height:14px;cursor:s-resize;filter:Alpha(Opacity=20);-moz-opacity:0.2;opacity: 0.2}
.Gooflow_extend_right:hover{background-color:#999;border:#fff 1px solid}
.Gooflow_extend_bottom:hover{background-color:#999;border:#fff 1px solid}

/*区域分组（泳道）的样式*/
.GooFlow_work_group{cursor:default;position:absolute;overflow:hidden;top:0;left:0}
.GooFlow_area{cursor:default;position:absolute;overflow:hidden;}
.GooFlow_area .lock{cursor:default;}
.GooFlow_area .bg{cursor:move;filter:Alpha(Opacity=30);-moz-opacity:0.3;opacity: 0.3;}
.GooFlow_area.lock .bg{cursor:default;}
.GooFlow_area label{cursor:text;top:0;left:23px;position:absolute;display:block;}
.GooFlow_area.lock label{cursor:default;}
.GooFlow_area i{top:2px;left:2px;width:18px;height:20px;position:absolute;cursor:pointer;}
.GooFlow_area i:before{content:"\e6bd"}
.GooFlow_area.area_red .bg{border:1px solid red;background-color:#FF7865}
.GooFlow_area.area_red label,.GooFlow_area.area_red i{color:red;}
.GooFlow_area.area_yellow .bg{border:1px solid #CD925A;background-color:#FFD564}
.GooFlow_area.area_yellow label,.GooFlow_area.area_yellow i{color:#FFBA1D;}
.GooFlow_area.area_blue .bg{border:1px solid #347BB1;background-color:#549CDE}
.GooFlow_area.area_blue label,.GooFlow_area.area_blue i{color:#347BB1;}
.GooFlow_area.area_green .bg{border:1px solid green;background-color:#84CA04}
.GooFlow_area.area_green label,.GooFlow_area.area_green i{color:green;}

/*画连线区域所需要的样式*/
.GooFlow_work svg{display:block;position:absolute;top:0;left:0}
.GooFlow_work v\:group{position:relative;display:block}
.GooFlow_work v\:group v\:line{overflow:visible}
.GooFlow_work v\:group v\:polyline{overflow:visible}
.GooFlow_work v\:group div{cursor:text;position:absolute;overflow:visible;display:inline;float:left;white-space: nowrap}
.GooFlow_work .draw{color:#ff8800}

/*各种节点样式*/
.GooFlow_item{
	position:absolute;background:#A1DCEB;padding:1px;
	border-radius:3px;background-color:#C1DCFC;box-shadow:1px 1px 2px rgba(99,99,99,2);
}
.GooFlow table{padding:1px 2px;border-radius:2px}
.GooFlow td,.GooFlow td div{ vertical-align:middle;text-align:center;padding:0;cursor:default;word-wrap:break-word;word-break:break-all}
.GooFlow .ico{width:18px;cursor:move;text-align:center; vertical-align: middle;}
.GooFlow .ico i{filter:Alpha(Opacity=30);-moz-opacity:0.3;opacity:0.3;color:#000;margin:0 auto;}

.GooFlow .item_round{border-radius:13px; overflow:visible}
.GooFlow .item_round table{border:0;padding:3px;width:26px;height:26px}
.GooFlow .item_round .span{
	display:block;text-align:center; position:absolute;top:100%;left:-100%;width:300%;overflow:visible;
	padding:0;cursor:default;word-wrap: break-word;word-break:break-all
}
.GooFlow .item_mix{background:#B6F700;color:#fff}
.GooFlow .item_focus{border:#3892D3 1px solid !important;padding:0;z-index:5}
.GooFlow .item_focus table{margin:0 !important;}
.GooFlow .item_focus.item_round .span{
	display:block;text-align:center; position:absolute;margin-top:1px;overflow:visible;
	padding:0;cursor:default;word-wrap: break-word;word-break:break-all;
}
.GooFlow .item_mark{border:#ff8800 2px solid;padding:0}
.GooFlow .item_mark table{margin:-1px}
.GooFlow .item_mark td{cursor:crosshair}
.GooFlow .item_mark.item_round .span{padding-top:2px}

/*编辑时一些工具的页面特效*/
.GooFlow textarea{position:absolute;border:#3892D3 1px solid;display:none;overflow-y:visible;width:100px;z-index:10001}
.GooFlow div .rs_right{overflow:hidden;position:absolute;right:-1px;top:-1px;height:100%;width:6px;cursor:w-resize}
.GooFlow div .rs_bottom{overflow:hidden;position:absolute;left:-1px;bottom:-1px;width:100%;height:6px;cursor:n-resize}
.GooFlow div .rs_rb{
    position:absolute;right:-1px;bottom:0;width:10px;height:9px;filter:Alpha(Opacity=70);-moz-opacity:0.7;opacity:0.7;
    font-family:"iconflow" !important;font-size:12px;color:#475669;line-height:1;overflow:hidden;cursor:pointer;cursor:nw-resize;
}
.GooFlow div .rs_rb:before{content:"\e6b7";}
.GooFlow div .rs_close{
    position:absolute;right:-1px;top:1px;width:10px;height:9px;filter:Alpha(Opacity=70);-moz-opacity:0.7;opacity:0.7;
    font-family:"iconflow" !important;font-size:12px;color:#475669;line-height:1;overflow:hidden;cursor:pointer;
}
.GooFlow div .rs_close:before{content:"\e674";}
.GooFlow .rs_ghost{
    position:absolute;display:none;overflow:hidden;border:#8492A6 1px dashed; background:#E5E9F2;
    filter:Alpha(Opacity=50);-moz-opacity:0.5;opacity: 0.5;z-index:10
}
.GooFlow .GooFlow_line_oper{
	width:82px;height:20px;background:#E5E9F2;border:#8492A6 1px solid;position:absolute;
	filter:Alpha(Opacity=50);-moz-opacity:0.5;opacity: 0.5;z-index:10000;
}
.GooFlow .GooFlow_line_mp{
	width:9px;height:9px;filter:Alpha(Opacity=40);-moz-opacity:0.4;opacity:0.4;overflow:hidden;
	position:absolute;z-index:9999;background:#333;cursor:crosshair
}
.GooFlow_linemove{background-color:transparent;filter:Alpha(Opacity=50);-moz-opacity:0.5;opacity:0.5;overflow:hidden;position:absolute;z-index:9999;}
.GooFlow_line_oper i{display:inline-block;margin-left:2px;cursor:pointer;position:relative;}
.GooFlow .b_l1:before{content:"\e60d";color:#1F2D3D}
.GooFlow .b_l2:before{content:"\e60e";color:#1F2D3D}
.GooFlow .b_l3:before{content:"\e601";color:#1F2D3D}
.GooFlow .b_x:before{content:"\e61a";color:red}


/*以下为图标样式（固定大小18px*18px，矢量字体大小16px），用户可自定义扩展自己的新矢量图标字体，写法参照以下的内容*/
@font-face {font-family: "iconflow";
  src: url('fonts/iconflow.eot?t=1494321407539'); /* IE9*/
  src: url('fonts/iconflow.eot?t=1494321407539#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url('fonts/iconflow.woff?t=1494321407539') format('woff'), /* chrome, firefox */
  url('fonts/iconflow.ttf?t=1494321407539') format('truetype'), /* chrome, firefox, opera, Safari, Android, iOS 4.2+*/
  url('fonts/iconflow.svg?t=1494321407539#iconflow') format('svg'); /* iOS 4.1- */
}
.GooFlow_area i,.GooFlow .GooFlow_line_oper i,.GooFlow_tool i,.GooFlow_head i,.GooFlow_item .ico i{
  font-family:"iconflow" !important;
  font-size:18px;line-height:20px;
  font-style:normal;
  -webkit-font-smoothing: antialiased;
  -webkit-text-stroke-width: 0.2px;
  -moz-osx-font-smoothing: grayscale;
}
/*自带的一些节点矢量图标样式*/
.GooFlow .ico_cursor:before{ content:"\e602"; }
.GooFlow .ico_start:before{ content:"\e700"; }
.GooFlow .ico_end:before{ content:"\e609"; }
.GooFlow .ico_fork:before{ content:"\e60c"; }
.GooFlow .ico_join:before{ content:"\e606"; }
.GooFlow .ico_direct:before{ content:"\e605"; }
.GooFlow .ico_dashed:before{ content:"\e675"; }
.GooFlow .ico_group:before{ content:"\e663"; }
.GooFlow .ico_complex:before{ content:"\e872"; }
/*.GooFlow .ico_complex{background:url(assets/img/gooflow_icon.png) no-repeat -116px -20px;opacity:1 !important;filter:Alpha(Opacity=70) !important;}*/
.GooFlow .ico_node:before{ content:"\e678"; }
.GooFlow .ico_task:before{ content:"\e6af"; }
/*.GooFlow .ico_task{background:url(assets/img/gooflow_icon.png) no-repeat 2px -45px;opacity:1 !important;filter:Alpha(Opacity=70) !important;}*/
.GooFlow .ico_chat:before{ content:"\e61b"; }
.GooFlow .ico_state:before{ content:"\e633"; }
.GooFlow .ico_plug:before{ content:"\e66c"; }
.GooFlow .ico_menu:before{ content:"\e649"; }
.GooFlow .ico_sound:before{ content:"\e62b"; }

/*以下是内部用头部工具栏按钮专用的样式*/
.GooFlow .ico_open:before{ content:"\e7a0";color:#FFD300 }
.GooFlow .ico_new:before{ content:"\e659"; }
.GooFlow .ico_reload:before{ content:"\e607";color:#669900 }
.GooFlow .ico_save:before{ content:"\e63d";color:#0099cc }
.GooFlow .ico_undo:before{ content:"\e673";color:#ff8800 }
.GooFlow .ico_redo:before{ content:"\e672";color:#ff8800 }
.GooFlow .ico_print:before{ content:"\e671"; }