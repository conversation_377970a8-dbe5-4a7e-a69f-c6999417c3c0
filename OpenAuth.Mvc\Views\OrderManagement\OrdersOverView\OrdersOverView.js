angular.module('myApp').controller('orderManagement.ordersOverView.ordersOverView', [
    '$scope', '$uibModal', '$timeout', '$http',
    function ($scope, $uibModal, $timeout, $http) {
        var vm = this;
        $scope.$on('$viewContentLoaded', function () {
            App.initAjax();
        });
        vm.isTSW = false;
        vm.requestParams = {
            type: "",
            pallets: 0,
            quantity: 0,
            record: 0,
            orderStyle: null,
            isNotCompleted: true,
        };
        vm.isXZCK = false;
        vm.isNewCreateOrder = false;
        vm.isNewCreateInboundOrder = false;
        var gridApi;
        //列表是否要更新标志
        vm.isGridUpDate = false;
        vm.csCode = "";
        vm.isDailyTUSM = false;
        vm.isOutbound = false;
        vm.isTUS = false;
        vm.isInbound = false;

        vm.orderStyleDropDownList = [];
        vm.locationDropdown = [];
        vm.warehouseDropdown = [];
        vm.ConsigneeDropdown = [];
        vm.specialFilter = function () {
            var param = {};
            if (vm.isNewCreateOrder) {

                vm.requestParams.orderType = "Outbound delivery"
                param.orderType = vm.requestParams.orderType;
                param.isNew = true;
                vm.isNewCreateOrder = false;
                return param;
            }
            if (vm.isNewCreateInboundOrder) {
                vm.requestParams.orderType = "Inbound"
                param.orderType = vm.requestParams.orderType;
                param.isNew = true;
                vm.isNewCreateInboundOrder = false;
                return param;
            }

            param.orderNo = vm.requestParams.orderNo;
            param.orderType = vm.requestParams.orderType;
            param.piNo = vm.requestParams.piNo;
            param.orderStartDate = vm.requestParams.orderStartDate;
            param.orderEndDate = vm.requestParams.orderEndDate;
            param.etaStartDate = vm.requestParams.etaStartDate;
            param.etaEndDate = vm.requestParams.etaEndDate;
            param.billNo = vm.requestParams.mbl;
            param.containerNo = vm.requestParams.containerNo;
            param.inboundStartDate = vm.requestParams.inboundStartDate;
            param.inboundEndDate = vm.requestParams.inboundEndDate;
            param.outboundStartDate = vm.requestParams.outboundStartDate;
            param.outboundEndDate = vm.requestParams.outboundEndDate;
            param.lotNo = vm.requestParams.lotNo;
            param.transport = vm.requestParams.transport;
            param.ccfStartDate = vm.requestParams.ccfStartDate;
            param.ccfEndDate = vm.requestParams.ccfEndDate;
            param.bodStartDate = vm.requestParams.bodStartDate;
            param.bodEndDate = vm.requestParams.bodEndDate;
            param.customerName = vm.requestParams.consignee;
            param.warehouseLocation = vm.requestParams.location;
            param.opStartDate = vm.requestParams.opStartDate;
            param.opEndDate = vm.requestParams.opEndDate;
            param.warehouseCode = vm.requestParams.warehouseCode;
            param.isNotCompleted = vm.requestParams.isNotCompleted;
            return param;
        }

        vm.init = function () {
            $("#orderStartDate").datepicker({
                autoclose: true, //选择后自动关闭
                clearBtn: true,//清除按钮
                format: "yyyy-mm-dd",//日期格式
            });
            $("#orderEndDate").datepicker({
                autoclose: true, //选择后自动关闭
                clearBtn: true,//清除按钮
                format: "yyyy-mm-dd",//日期格式
            });
            $("#etaStartDate").datepicker({
                autoclose: true, //选择后自动关闭
                clearBtn: true,//清除按钮
                format: "yyyy-mm-dd",//日期格式
            });
            $("#etaEndDate").datepicker({
                autoclose: true, //选择后自动关闭
                clearBtn: true,//清除按钮
                format: "yyyy-mm-dd",//日期格式
            });
            $("#inboundStartDate").datepicker({
                autoclose: true, //选择后自动关闭
                clearBtn: true,//清除按钮
                format: "yyyy-mm-dd",//日期格式
            });
            $("#inboundEndDate").datepicker({
                autoclose: true, //选择后自动关闭
                clearBtn: true,//清除按钮
                format: "yyyy-mm-dd",//日期格式
            });
            $("#outboundStartDate").datepicker({
                autoclose: true, //选择后自动关闭
                clearBtn: true,//清除按钮
                format: "yyyy-mm-dd",//日期格式
            });
            $("#outboundEndDate").datepicker({
                autoclose: true, //选择后自动关闭
                clearBtn: true,//清除按钮
                format: "yyyy-mm-dd",//日期格式
            });
            $("#ccfStartDate").datepicker({
                autoclose: true, //选择后自动关闭
                clearBtn: true,//清除按钮
                format: "yyyy-mm-dd",//日期格式
            });
            $("#ccfEndDate").datepicker({
                autoclose: true, //选择后自动关闭
                clearBtn: true,//清除按钮
                format: "yyyy-mm-dd",//日期格式
            });
            $("#bodStartDate").datepicker({
                autoclose: true, //选择后自动关闭
                clearBtn: true,//清除按钮
                format: "yyyy-mm-dd",//日期格式
            });
            $("#bodEndDate").datepicker({
                autoclose: true, //选择后自动关闭
                clearBtn: true,//清除按钮
                format: "yyyy-mm-dd",//日期格式
            });
            $("#opStartDate").datepicker({
                autoclose: true, //选择后自动关闭
                clearBtn: true,//清除按钮
                format: "yyyy-mm-dd",//日期格式
            });
            $("#opEndDate").datepicker({
                autoclose: true, //选择后自动关闭
                clearBtn: true,//清除按钮
                format: "yyyy-mm-dd",//日期格式
            });
            $.post('/UserSession/GetUserBasic',
                function (data) {
                    if (data.xzck) {
                        vm.isXZCK = true;
                    }
                }
            );
            vm.initData();

            //判断是否有限制仓库
            //如果有仅限仓库，隐藏 Warehouse Code 列
            //vm.isTSW = true;
        }

        vm.initGrid = function () {
            new agGrid.createGrid(document.getElementById('agGrid'), vm.gridOptions);

        }

        vm.initData = function () {
            $.post('/UserSession/GetUserBasic',
                function (data) {
                    vm.csCode = data.csCode;
                    vm.isDailyTUSM = vm.csCode == "TUSM" ? true : false;;
                    vm.isDailyTUM = vm.csCode == "TUM" ? true : false;;
                    vm.isTUS = vm.csCode == "TUS" ? true : false;;


                }
            );

            $http({
                method: 'POST',
                url: '/Trucking/GetTransportAgentDropdown',
                data: {
                    fieldName: 'Location'

                },
            }).then(function (response) {
                if (response.data.code === 200) {
                    vm.locationDropdown = response.data.data;
                } else {
                    myAlert.message.error(response.data.msg);
                }
            }).catch(function (error) {
                console.log(error);
            });
            $http({
                method: 'POST',
                url: '/Trucking/GetTransportAgentDropdown',
                data: {
                    fieldName: 'Warehouse Code'

                },
            }).then(function (response) {
                if (response.data.code === 200) {
                    vm.warehouseDropdown = response.data.data;
                } else {
                    myAlert.message.error(response.data.msg);
                }
            }).catch(function (error) {
                console.log(error);
            });

            $http({
                method: 'POST',
                url: '/Trucking/GetTransportAgentDropdown',
                data: {
                    fieldName: 'Consignee Name'
                },
            }).then(function (response) {
                if (response.data.code === 200) {
                    vm.ConsigneeDropdown = response.data.data;
                } else {
                    myAlert.message.error(response.data.msg);
                }
            }).catch(function (error) {
                console.log(error);
            });

            $http({
                method: 'POST',
                url: '/Trucking/GetOrderTypeDropdown',
                data: {
                    fieldName: 'OrderStyle'
                },
            }).then(function (response) {
                if (response.data.code === 200) {
                    vm.orderStyleDropDownList = response.data.data;
                    if (vm.orderStyleDropDownList.length == 0) {
                        vm.orderStyleDropDownList = [{ text: "Inbound", value: "Inbound" }, { text: "Outbound delivery", value: "Outbound delivery" }]
                    }
                    vm.requestParams.orderType = vm.orderStyleDropDownList[0].value;
                    //判断其中是否有Outbound delivery,如果有，默认选择Outbound delivery
                    for (var i = 0; i < vm.orderStyleDropDownList.length; i++) {
                        if (vm.orderStyleDropDownList[i].value == "Outbound delivery") {
                            vm.requestParams.orderType = "Outbound delivery";
                            vm.isOutbound = true;
                        }
                        if (vm.orderStyleDropDownList[i].value == "Inbound") {
                            vm.isInbound = true;
                        }
                    }

                    //刷新列表
                    vm.initGrid();
                } else {
                    myAlert.message.error(response.data.msg);
                }
            }).catch(function (error) {
                console.log(error);
            });
        }
        function buttonCellRenderer(params) {
            var container = document.createElement('div');
            container.classList.add('button-container-ag');
            var btnView = document.createElement('button');
            btnView.innerHTML = '<i class="fa fa-eye"></i> View';
            btnView.classList.add('btn', 'btn-primary', 'btn-sm');

            // 添加修改按钮事件
            btnView.addEventListener('click', function () {

                var data = params.data;
                //打开详情页面
                //跳转页面显示详细弹窗
                var modalInstance = $uibModal.open({
                    templateUrl: '/OrdersOverView/OrderDetail',
                    controller: 'orderManagement.ordersOverView.orderDetail as vm',
                    backdrop: 'static',
                    size: 'full',
                    resolve: {
                        options: function () {
                            return {
                                orderNo: data.orderNo,
                            };
                        }
                    }
                });
                modalInstance.result.then(function (result) {
                }, function (reason) {
                    // 模态框取消关闭事件
                }).finally(function () {

                });
            });
            container.appendChild(btnView);
            // 创建Warehouse按钮
            if (params.data.orderType == "Outbound delivery" || params.data.orderType == "Inbound") {
                var btnWare = document.createElement('button');
                btnWare.innerHTML = '<i class="bi bi-pencil-fill"></i> Warehouse';
                btnWare.classList.add('btn', 'btn-primary', 'btn-sm');

                // 添加修改按钮事件
                btnWare.addEventListener('click', function () {
                    //页面跳转
                    //获取当前选择的行数据
                    var data = params.data;
                    //打开弹窗
                    var modalInstance = $uibModal.open({
                        templateUrl: '/OrdersOverView/OrderWarehouse',
                        controller: 'orderManagement.ordersOverView.orderWarehouse as vm',
                        backdrop: 'static',
                        size: 'full',
                        resolve: {
                            options: function () {
                                return {
                                    dataItem: data,
                                    //isComplete: data.operationCompletionTime != null ? true : false,
                                };
                            }
                        }
                    });
                    modalInstance.result.then(function (result) {
                    }, function (reason) {
                        // 模态框取消关闭事件
                    }).finally(function () {

                    });

                });
                container.appendChild(btnWare);
            }


            // 返回
            return container;
        }
        var columns = [
            {
                headerName: "Actions",
                field: "actions",
                cellRenderer: 'buttonCellRenderer',
                filter: false,
                sortable: false,
                filterParams: {
                    suppressSorting: false,
                },
                suppressMovable: true,
                suppressHeaderMenuButton: true,
                suppressColumnsToolPanel: true, // 禁用列工具面板
                lockPinned: true,
                pinned: 'right'  // 将此列固定在右侧
            },
            //可以通过API来移动列的位置
            {
                headerName: "Order No.",
                field: 'orderNo',
            },
            {
                headerName: "Order Type",
                field: 'orderType',
            },
            {
                headerName: "Order Date",
                field: 'orderDate',
                valueGetter: function (params) {
                    if (!params.data || !params.data.orderDate) {
                        return null;
                    }
                    return new Date(params.data.orderDate);
                },
                valueFormatter: function (params) {
                    if (!params.value) {
                        return "";
                    }
                    return moment(params.value).format('YYYY-MM-DD HH:mm:ss');
                },

            },
            {
                headerName: "Customer Code",
                field: 'customerCode',
            },
            {
                headerName: "Location",
                field: 'warehouseLocation',
            },
            {
                headerName: "PI No.",
                field: 'piNo',
            },
            {
                headerName: "LOTNO",
                field: 'lotNo',
            },
            {
                headerName: "Transport Agent",
                field: 'transportationAgent',
            },
            {
                headerName: "Recipient Address",
                field: 'address',
            },
            {
                headerName: "Customer Required Goods Reciving Date",
                field: 'receiptDate',
                valueGetter: function (params) {
                    if (!params.data || !params.data.receiptDate) {
                        return null;
                    }
                    return new Date(params.data.receiptDate);
                },
                valueFormatter: function (params) {
                    if (!params.value) {
                        return "";
                    }
                    return moment(params.value).format('YYYY-MM-DD');
                },
            },
            {
                headerName: "Special requirement",
                field: 'trackingRemarks',
            },
            {
                headerName: "Delivery type",
                field: 'deliveryType',
            },
            {
                headerName: "KMS",
                field: 'kilometers',
            },
            {
                headerName: "Truck",
                field: 'deliveryVehicleType',
            },
            {
                headerName: "Warehouse outbound date",
                field: 'outboundCompletionTime',
                valueGetter: function (params) {
                    if (!params.data || !params.data.outboundCompletionTime) {
                        return null;
                    }
                    return new Date(params.data.outboundCompletionTime);
                },
                valueFormatter: function (params) {
                    if (!params.value) {
                        return "";
                    }
                    return moment(params.value).format('YYYY-MM-DD');
                },
            },
            {
                headerName: "Delivery completion time",
                field: 'deliveryCompletionTime',
                valueGetter: function (params) {
                    if (!params.data || !params.data.deliveryCompletionTime) {
                        return null;
                    }
                    return new Date(params.data.deliveryCompletionTime);
                },
                valueFormatter: function (params) {
                    if (!params.value) {
                        return "";
                    }
                    return moment(params.value).format('YYYY-MM-DD HH:mm:ss');
                },
            },
            {
                headerName: "POD receipt completion time",
                field: 'podReturnTime',
                valueGetter: function (params) {
                    if (!params.data || !params.data.podReturnTime) {
                        return null;
                    }
                    return new Date(params.data.podReturnTime);
                },
                valueFormatter: function (params) {
                    if (!params.value) {
                        return "";
                    }
                    return moment(params.value).format('YYYY-MM-DD HH:mm:ss');
                },
            },
            {
                headerName: "DN No.",
                field: 'managementNumber',
            },
            {
                headerName: "Warehouse Code",
                field: 'warehouseAgent',
            },
            {
                headerName: "Description",
                field: 'productName',
            },
            {
                headerName: "Cargo class",
                field: 'productCategory',
            },
            {
                headerName: "Type",
                field: 'productSpecification',
            },
            {
                headerName: "Power",
                field: 'power',
            },
            {
                headerName: "Quantity",
                field: 'quantity',
            },
            {
                headerName: "Total Vol",
                field: 'totalPower',
            },
            {
                headerName: "Pallet QTY",
                field: 'palletQty',
            },
            {
                headerName: "Chargeable Weight",
                field: 'grossWeight',
            },
            {
                headerName: "Container No.",
                field: 'containerNo',
            },
            {
                headerName: "Container QTY",
                field: 'containerQty',
            },
            {
                headerName: "Container type",
                field: 'containerType',
            },
            {
                headerName: "Cargo Value/ EUR",
                field: 'cargoValue',
            },
            {
                headerName: "Consignee picks up",
                field: 'consigneePicksUp',
            },
            {
                headerName: "Date of receiving order",
                field: 'orderInstructionReceiptDate',
                valueGetter: function (params) {
                    if (!params.data || !params.data.orderInstructionReceiptDate) {
                        return null;
                    }
                    return new Date(params.data.orderInstructionReceiptDate);
                },
                valueFormatter: function (params) {
                    if (!params.value) {
                        return "";
                    }
                    return moment(params.value).format('YYYY-MM-DD');
                },
            },
            {
                headerName: "Cancel order",
                field: 'cancelOrder',
            },
            {
                headerName: "Send order to the supplier",
                field: 'instructionSendingTime',
                valueGetter: function (params) {
                    if (!params.data || !params.data.instructionSendingTime) {
                        return null;
                    }
                    return new Date(params.data.instructionSendingTime);
                },
                valueFormatter: function (params) {
                    if (!params.value) {
                        return "";
                    }
                    return moment(params.value).format('YYYY-MM-DD HH:mm:ss');
                },
            },
            {
                headerName: "Settlement completion time",
                field: 'settlementCompletionTime',
                valueGetter: function (params) {
                    if (!params.data || !params.data.settlementCompletionTime) {
                        return null;
                    }
                    return new Date(params.data.settlementCompletionTime);
                },
                valueFormatter: function (params) {
                    if (!params.value) {
                        return "";
                    }
                    return moment(params.value).format('YYYY-MM-DD HH:mm:ss');
                },
            },
            {
                headerName: "Operation completion time",
                field: 'operationCompletionTime',
                valueGetter: function (params) {
                    if (!params.data || !params.data.operationCompletionTime) {
                        return null;
                    }
                    return new Date(params.data.operationCompletionTime);
                },
                valueFormatter: function (params) {
                    if (!params.value) {
                        return "";
                    }
                    return moment(params.value).format('YYYY-MM-DD HH:mm:ss');
                },
            },


        ];

        var columns2 = [
            {
                headerName: "Actions",
                field: "actions",
                cellRenderer: 'buttonCellRenderer',
                filter: false,
                sortable: false,
                filterParams: {
                    suppressSorting: false,
                },
                suppressMovable: true,
                suppressHeaderMenuButton: true,
                suppressColumnsToolPanel: true, // 禁用列工具面板
                lockPinned: true,
                pinned: 'right'  // 将此列固定在右侧
            },
            //如果有限制仓库，进行更改
            {
                headerName: "Order No.",
                field: 'orderNo',
            },
            {
                headerName: "Order Type",
                field: 'orderType',
            },
            {
                headerName: "Order Date",
                field: 'orderDate',
                valueGetter: function (params) {
                    if (!params.data || !params.data.orderDate) {
                        return null;
                    }
                    return new Date(params.data.orderDate);
                },
                valueFormatter: function (params) {
                    if (!params.value) {
                        return "";
                    }
                    return moment(params.value).format('YYYY-MM-DD HH:mm:ss');
                },
            },
            {
                headerName: "Customer Code",
                field: 'customerCode',
            },
            {
                headerName: "PI No.",
                field: 'piNo',
            },
            {
                headerName: "LOT NO.",
                field: 'lotNo',
            },
            {
                headerName: "Location",
                field: 'warehouseLocation',
            },
            {
                headerName: "Warehouse Code",
                field: 'warehouseAgent',
            },
            {
                headerName: "Description",
                field: 'productName',
            },
            {
                headerName: "Cargo class",
                field: 'productCategory',
            },
            {
                headerName: "Power",
                field: 'power',
            },
            {
                headerName: "Quantity",
                field: 'quantity',
            },
            {
                headerName: "Total Vol",
                field: 'totalPower',
            },
            {
                headerName: "Pallet QTY",
                field: 'palletQty',
            },
            {
                headerName: "Volume",
                field: 'volume',
            },
            {
                headerName: "Container No.",
                field: 'containerNo',
            },
            {
                headerName: "Container QTY",
                field: 'containerQty',
            },
            {
                headerName: "Container type",
                field: 'containerType',
            },
            {
                headerName: "Port of Discharge",
                field: 'destinationPort',
            },
            {
                headerName: "M/BL",
                field: 'billNo',
            },
            {
                headerName: "Booking Date",
                field: 'bookingDate',
                valueGetter: function (params) {
                    if (!params.data || !params.data.bookingDate) {
                        return null;
                    }
                    return new Date(params.data.bookingDate);
                },
                valueFormatter: function (params) {
                    if (!params.value) {
                        return "";
                    }
                    return moment(params.value).format('YYYY-MM-DD');
                },
            },
            {
                headerName: "ETD",
                field: 'etd',
            },
            {
                headerName: "ETA",
                field: 'eta',
            },
            {
                headerName: "ATA",
                field: 'ata',
            },
            {
                headerName: "Transport Agent",
                field: 'transportationAgent',
            },
            {
                headerName: "Recipient Address",
                field: 'address',
            },
            {
                headerName: "Special requirement",
                field: 'trackingRemarks',
            },
            {
                headerName: "Delivery type",
                field: 'deliveryType',
            },
            {
                headerName: "Truck",
                field: 'deliveryVehicleType',
            },
            {
                headerName: "Date of receiving order",
                field: 'orderInstructionReceiptDate',
                valueGetter: function (params) {
                    if (!params.data || !params.data.orderInstructionReceiptDate) {
                        return null;
                    }
                    return new Date(params.data.orderInstructionReceiptDate);
                },
                valueFormatter: function (params) {
                    if (!params.value) {
                        return "";
                    }
                    return moment(params.value).format('YYYY-MM-DD');
                },
            },
            {
                headerName: "Remark",
                field: 'remarks',
            },
            {
                headerName: "Cancel order",
                field: 'cancelOrder',
            },
            {
                headerName: "Send order to the supplier",
                field: 'instructionSendingTime',
                valueGetter: function (params) {
                    if (!params.data || !params.data.instructionSendingTime) {
                        return null;
                    }
                    return new Date(params.data.instructionSendingTime);
                },
                valueFormatter: function (params) {
                    if (!params.value) {
                        return "";
                    }
                    return moment(params.value).format('YYYY-MM-DD HH:mm:ss');
                },
            },
            {
                headerName: "Clearance release time",
                field: 'customsClearanceCompletionTime',
                valueGetter: function (params) {
                    if (!params.data || !params.data.customsClearanceCompletionTime) {
                        return null;
                    }
                    return new Date(params.data.customsClearanceCompletionTime);
                },
                valueFormatter: function (params) {
                    if (!params.value) {
                        return "";
                    }
                    return moment(params.value).format('YYYY-MM-DD HH:mm:ss');
                },
            },
            {
                headerName: "Actual time of inbound",
                field: 'inboundCompletionTime',
                valueGetter: function (params) {
                    if (!params.data || !params.data.inboundCompletionTime) {
                        return null;
                    }
                    return new Date(params.data.inboundCompletionTime);
                },
                valueFormatter: function (params) {
                    if (!params.value) {
                        return "";
                    }
                    return moment(params.value).format('YYYY-MM-DD HH:mm:ss');
                },
            },
            {
                headerName: "Settlement completion time",
                field: 'settlementCompletionTime',
                valueGetter: function (params) {
                    if (!params.data || !params.data.settlementCompletionTime) {
                        return null;
                    }
                    return new Date(params.data.settlementCompletionTime);
                },
                valueFormatter: function (params) {
                    if (!params.value) {
                        return "";
                    }
                    return moment(params.value).format('YYYY-MM-DD HH:mm:ss');
                },
            },
            {
                headerName: "Operation completion time",
                field: 'operationCompletionTime',
                valueGetter: function (params) {
                    if (!params.data || !params.data.operationCompletionTime) {
                        return null;
                    }
                    return new Date(params.data.operationCompletionTime);
                },
                valueFormatter: function (params) {
                    if (!params.value) {
                        return "";
                    }
                    return moment(params.value).format('YYYY-MM-DD HH:mm:ss');
                },
            },
        ];

        var column3 = [{
            headerName: "Actions",
            field: "actions",
            cellRenderer: 'buttonCellRenderer',
            filter: false,
            sortable: false,
            filterParams: {
                suppressSorting: false,
            },
            suppressMovable: true,
            suppressHeaderMenuButton: true,
            suppressColumnsToolPanel: true, // 禁用列工具面板
            lockPinned: true,
            pinned: 'right'  // 将此列固定在右侧
        },
        {
            headerName: "Order No.",
            field: 'orderNo',
            },
            {
                headerName: "MBL",
                field: 'billNo',
            },
            {
                headerName: "Container No.",
                field: 'containerNo',
            },
            {
                headerName: "ETA",
                field: 'eta',
                valueGetter: function (params) {
                    if (!params.data || !params.data.eta) {
                        return null;
                    }
                    return new Date(params.data.eta);
                },
                valueFormatter: function (params) {
                    if (!params.value) {
                        return "";
                    }
                    return moment(params.value).format('YYYY-MM-DD HH:mm:ss');
                },
            },
            {
                headerName: "Arrival Notice Received",
                field: 'cargoNotificationReceivedTime',
                valueGetter: function (params) {
                    if (!params.data || !params.data.cargoNotificationReceivedTime) {
                        return null;
                    }
                    return new Date(params.data.cargoNotificationReceivedTime);
                },
                valueFormatter: function (params) {
                    if (!params.value) {
                        return "";
                    }
                    return moment(params.value).format('YYYY-MM-DD HH:mm:ss');
                },
            },
            {
                headerName: "Customs Clearance Docs Received",
                field: 'receivedCustomsDocumentsTime',
                valueGetter: function (params) {
                    if (!params.data || !params.data.receivedCustomsDocumentsTime) {
                        return null;
                    }
                    return new Date(params.data.receivedCustomsDocumentsTime);
                },
                valueFormatter: function (params) {
                    if (!params.value) {
                        return "";
                    }
                    return moment(params.value).format('YYYY-MM-DD HH:mm:ss');
                },
            },
            {
                headerName: "Customs Clearance Docs Sent",
                field: 'sentCustomsDocumentsTime',
                valueGetter: function (params) {
                    if (!params.data || !params.data.sentCustomsDocumentsTime) {
                        return null;
                    }
                    return new Date(params.data.sentCustomsDocumentsTime);
                },
                valueFormatter: function (params) {
                    if (!params.value) {
                        return "";
                    }
                    return moment(params.value).format('YYYY-MM-DD HH:mm:ss');
                },
            },
            {
                headerName: "AD/CVD",
                field: 'cvdCaseNo',
            },
            {
                headerName: "Freight Release",
                field: 'carrierReleaseTime',
                valueGetter: function (params) {
                    if (!params.data || !params.data.carrierReleaseTime) {
                        return null;
                    }
                    return new Date(params.data.carrierReleaseTime);
                },
                valueFormatter: function (params) {
                    if (!params.value) {
                        return "";
                    }
                    return moment(params.value).format('YYYY-MM-DD HH:mm:ss');
                },
            },
            {
                headerName: "Customs Clearance Finished",
                field: 'customsClearanceCompletionTime',
                valueGetter: function (params) {
                    if (!params.data || !params.data.customsClearanceCompletionTime) {
                        return null;
                    }
                    return new Date(params.data.customsClearanceCompletionTime);
                },
                valueFormatter: function (params) {
                    if (!params.value) {
                        return "";
                    }
                    return moment(params.value).format('YYYY-MM-DD HH:mm:ss');
                },
            },
            {
                headerName: "7501 & 3461 Received",
                field: 'rev7501',
                valueGetter: function (params) {
                    if (!params.data || !params.data.rev7501) {
                        return null;
                    }
                    return new Date(params.data.rev7501);
                },
                valueFormatter: function (params) {
                    if (!params.value) {
                        return "";
                    }
                    return moment(params.value).format('YYYY-MM-DD HH:mm:ss');
                },
            },
            {
                headerName: "DO Received",
                field: 'doResceivedTime',
                valueGetter: function (params) {
                    if (!params.data || !params.data.doResceivedTime) {
                        return null;
                    }
                    return new Date(params.data.doResceivedTime);
                },
                valueFormatter: function (params) {
                    if (!params.value) {
                        return "";
                    }
                    return moment(params.value).format('YYYY-MM-DD HH:mm:ss');
                },
            },
            {
                headerName: "DO Sent",
                field: 'doSendingTime',
                valueGetter: function (params) {
                    if (!params.data || !params.data.doSendingTime) {
                        return null;
                    }
                    return new Date(params.data.doSendingTime);
                },
                valueFormatter: function (params) {
                    if (!params.value) {
                        return "";
                    }
                    return moment(params.value).format('YYYY-MM-DD HH:mm:ss');
                },
            },
            {
                headerName: "East2West Ref #",
                field: 'ewRef',
            },
            {
                headerName: "DO Confirmation",
                field: 'confirmedDOSendingTime',
                valueGetter: function (params) {
                    if (!params.data || !params.data.confirmedDOSendingTime) {
                        return null;
                    }
                    return new Date(params.data.confirmedDOSendingTime);
                },
                valueFormatter: function (params) {
                    if (!params.value) {
                        return "";
                    }
                    return moment(params.value).format('YYYY-MM-DD HH:mm:ss');
                },
            },

        ]

        vm.gridOptions = {
            rowModelType: 'serverSide',
            columnDefs: columns,
            //默认20行
            paginationPageSize: 20,
            defaultColDef: {
                sortable: true,
                filter: "agSetColumnFilter",
                filterParams: {
                    values: getUniqueValues,
                    excelMode: 'windows',
                    refreshValuesOnOpen: false,
                },
                suppressColumnsToolPanel: true,
            },
            getRowId: function (params) {
                const id = params.data.orderNo; // 假设数据中有 `id` 字段
                return id ? id.toString() : ''; // 如果 ID 存在，则转换为字符串；如果不存在，返回空字符串
            },
            pagination: true,
            suppressMovableColumns: true,
            rowSelection: "multiple",
            enableRangeSelection: true,
            components: {
                buttonCellRenderer: buttonCellRenderer
            },
            onPaginationChanged: function (event) {
                //if (event.newPage) {
                //    gridApi.setGridOption("defaultColDef", defaultColDef());
                //}
            },
            // 在 onGridReady 中使用公共数据源函数
            onGridReady: function (params) {
                gridApi = params.api;
                //var a = "Inbound";
                if (vm.isXZCK) {
                    gridApi.getColumn('productName').setVisible(false);

                } else {
                    gridApi.getColumn('warehouseAgent').setVisible(false);
                }

                //gridApi.getColumn('productName').setVisible(false);
                //if (a == "Inbound") {
                //    //隐藏仓库代理
                //} else {
                //    gridApi.moveColumns(["lotNo"], 17);
                //}
                const dataSource = {
                    getRows: function (params) {
                        createDataSourceV2('/OrdersOverView/GetOrderList', vm.specialFilter(), params);
                        vm.isGridUpDate = false;
                    }
                };
                // 设置服务器端数据源
                params.api.setGridOption("serverSideDatasource", dataSource);

            },
            //双击行事件
            onRowDoubleClicked: function (params) {
                //获取双击行的数据
                var data = params.data;
                //打开详情页面
                //跳转页面显示详细弹窗
                var modalInstance = $uibModal.open({
                    templateUrl: '/OrdersOverView/OrderDetail',
                    controller: 'orderManagement.ordersOverView.orderDetail as vm',
                    backdrop: 'static',
                    size: 'full',
                    resolve: {
                        options: function () {
                            return {
                                orderNo: data.orderNo,
                            };
                        }
                    }
                });
                modalInstance.result.then(function (result) {
                }, function (reason) {
                    // 模态框取消关闭事件
                }).finally(function () {

                });

            },
        }

        vm.changeOrderType = function () {
            vm.isGridUpDate = true;
        }

        vm.search = function () {
            //判断是否需要更新列表信息，如果不需要，则不刷新列表，执行重新查询即可
            if (vm.isGridUpDate) {
                //更新列信息
                //如果是“Inbound”
                if (vm.requestParams.orderType == "Inbound") {
                    gridApi.updateGridOptions({
                        columnDefs: columns2,
                    });
                    //是限制仓库的用户
                    if (vm.isXZCK) {

                        gridApi.getColumn('warehouseAgent').setVisible(false);
                    }

                } else if (vm.requestParams.orderType == "Customs clearance") {
                    gridApi.updateGridOptions({
                        columnDefs: column3,
                    });
                } else {
                    gridApi.updateGridOptions({
                        columnDefs: columns,
                    });
                    //是限制仓库的用户
                    if (vm.isXZCK) {
                        //gridApi.getColumn('productName').setVisible(false);
                        gridApi.getColumn('warehouseAgent').setVisible(false);
                        gridApi.moveColumns(["lotNo"], 16);
                    }

                }
            }
            gridApi.refreshServerSide();
            gridApi.onFilterChanged();

        }

        vm.resetting = function () {
            vm.requestParams = {};
            vm.isXZCK = true;
            vm.requestParams.orderType = vm.orderStyleDropDownList[0].value;
            vm.search();
        }

        vm.addOutBoundOrder = function () {
            myAlert.message.confirm("Are you sure to add a new outbound order?", function (isConfirmed) {
                if (isConfirmed) {
                    //请求接口新增OutBound订单
                    $http.post('/OrdersOverView/AddOutBoundOrder', {

                    }).then(function (response) {
                        if (response.data.code == 200) {
                            myAlert.message.info("Order creation successful：【" + response.data.data + "】");
                            //刷新列表
                            vm.isNewCreateOrder = true;
                            vm.search();
                        } else {
                            myAlert.message.error(response.data.data.msg);
                        }

                    });
                };
            });

        }

        vm.addInBoundOrder = function () {
            myAlert.message.confirm("Are you sure to add a new inbound order?", function (isConfirmed) {
                if (isConfirmed) {
                    //请求接口新增OutBound订单
                    $http.post('/OrdersOverView/AddInBoundOrder', {

                    }).then(function (response) {
                        console.log(response);
                        if (response.data.code == 200) {
                            myAlert.message.info("Order creation successful：【" + response.data.data + "】");
                            //刷新列表
                            vm.isNewCreateInboundOrder = true;
                            vm.search();
                        } else {
                            myAlert.message.error(response.data.data.msg);
                        }

                    });
                };
            });

        }

        vm.openDailyReport = function () {
            var modalInstance = $uibModal.open({
                templateUrl: '/OrdersOverView/DailyReport',
                controller: 'basicManagement.dailyReport.dailyReport as vm',
                backdrop: 'static',
                size: 'md',
                resolve: {
                    options: function () {
                        return {
                            //orderNo: data.orderNo,
                        };
                    }
                }
            });
            modalInstance.result.then(function (result) {
            }, function (reason) {
                // 模态框取消关闭事件
            }).finally(function () {

            });
        }

        vm.dailyReport = function () {
            vm.isBusy = true;
            myAlert.message.confirm("Are you sure to generate the daily report?", function (isConfirmed) {
                if (isConfirmed) {

                    //请求接口生成日报
                    $http({
                        method: 'POST',
                        url: '/OrdersOverView/EditDailyReport',
                        data: {

                        },
                    }).then(function (response) {

                        if (response.data.code == 200) {
                            myAlert.message.success(response.data.msg);
                        }
                    }).catch(function (error) {
                        console.log(error);
                    }).finally(function () {
                        vm.isBusy = false;
                    });
                } else {
                    vm.isBusy = false;
                    $scope.$apply();
                };
            });


        };

        vm.dailyReportModule = function () {
            myAlert.message.confirm("Are you sure to generate the daily report?", function (isConfirmed) {
                if (isConfirmed) {

                    //请求接口生成日报
                    $http({
                        method: 'POST',
                        url: '/OrdersOverView/EditDailyReport',
                        data: {
                            type: "module"
                        },
                    }).then(function (response) {

                        if (response.data.code == 200) {
                            myAlert.message.success(response.data.msg);
                        }
                    }).catch(function (error) {
                        console.log(error);
                    }).finally(function () {
                    });
                } else {
                    vm.isBusy = false;
                    $scope.$apply();
                };
            });


        };


        vm.dailyCustomsReport = function () {


            var modalInstance = $uibModal.open({
                templateUrl: '/OrdersTUS/CustomTUSReport',
                controller: 'warehouse.OrderManagement.tus.customTUSReport as vm',
                backdrop: 'static',
                size: 'lg',
                resolve: {
                    options: function () {
                        return {

                        };
                    }
                }
            });
            modalInstance.result.then(function (result) {
            }, function (reason) {
                // 模态框取消关闭事件
            }).finally(function () {

            });

        };

        vm.dailyReportMaterials = function () {
            myAlert.message.confirm("Are you sure to generate the daily report?", function (isConfirmed) {
                if (isConfirmed) {

                    //请求接口生成日报
                    $http({
                        method: 'POST',
                        url: '/OrdersOverView/EditDailyReport',
                        data: {
                            type: "materials"
                        },
                    }).then(function (response) {

                        if (response.data.code == 200) {
                            myAlert.message.success(response.data.msg);
                        }
                    }).catch(function (error) {
                        console.log(error);
                    }).finally(function () {
                    });
                } else {
                    vm.isBusy = false;
                    $scope.$apply();
                };
            });


        };


        vm.isDateValue = function (value) {
            if (value instanceof Date) {
                // 已经是 Date 类型
                return true;
            }

            // 如果是字符串类型，尝试将其转换为日期
            if (typeof value === 'string') {
                // 常见日期格式的正则表达式，可以根据需要修改
                const dateRegex = /^\d{4}-\d{2}-\d{2}$/;  // 比如 "2024-12-11"
                const isoDateRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}$/; // 比如 "2024-12-11T00:00:00"

                // 检查是否是ISO日期格式 (2024-12-11T00:00:00)
                if (isoDateRegex.test(value)) {
                    return true;
                }

                // 检查是否是简单日期格式 (2024-12-11)
                if (!dateRegex.test(value)) {
                    return false;
                }

                // 尝试解析并验证日期的有效性
                const date = new Date(value);

                if (isNaN(date.getTime())) {
                    // 解析失败或日期无效
                    return false;
                }

                return true;
            }
        }
        vm.export = function () {
            vm.busying = true;
            //请求接口获取数据
            $http({
                method: 'POST',
                url: '/OrdersOverView/GetOrderListByExcel',
                data: vm.specialFilter(),
            }).then(function (response) {
                if (response.data.code === 200) {

                    //获取到所有数据
                    var allData = response.data.data;
                    var workbook = new ExcelJS.Workbook();
                    var worksheet = workbook.addWorksheet('Sheet 1');
                    var gridColumns = [];
                    if (vm.requestParams.orderType == "Inbound") {
                        gridColumns = columns2;
                    } else if (vm.requestParams.orderType == "Customs clearance") {
                        gridColumns = column3;
                    } else {
                        gridColumns = columns;
                    }


                    var excelColumns = gridColumns.filter(x => x.headerName !="Actions").map(function (col) {
                        return {
                            header: col.headerName,  // 对应列的头部
                            key: col.field,          // 对应数据字段
                        };
                    });
                    worksheet.columns = excelColumns;
                    allData.forEach(function (item) {
                        worksheet.addRow(item);
                    });
                    columns.forEach(function (col, index) {
                        var maxLength = 0;
                        // 计算列头长度
                        maxLength = Math.max(maxLength, col.headerName.length);
                        // 设置列头的宽度
                        worksheet.getColumn(index + 1).width = maxLength + 5; // 加2是为了留白，避免内容紧挨列边框
                    });

                    worksheet.eachRow({ includeEmpty: true }, function (row, rowNumber) {
                        row.eachCell({ includeEmpty: true }, function (cell, colNumber) {
                            // 设置单元格的边框
                            cell.border = {
                                top: { style: 'thin' },
                                left: { style: 'thin' },
                                bottom: { style: 'thin' },
                                right: { style: 'thin' }
                            };
                            // 设置字体样式
                            cell.font = { name: 'Arial', size: 10 };
                            if (vm.isDateValue(cell.value)) {
                                // 确保 cell.value 是 Date 对象
                                let dateValue = cell.value;

                                // 如果是ISO日期字符串格式 (2024-12-11T00:00:00)，转换为Date对象
                                if (typeof cell.value === 'string' && /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}$/.test(cell.value)) {
                                    dateValue = new Date(cell.value);
                                }

                                // 使用 Intl.DateTimeFormat 格式化日期为 CST，包含时分秒
                                const formatter = new Intl.DateTimeFormat('zh-CN', {
                                    year: 'numeric',
                                    month: '2-digit',
                                    day: '2-digit',
                                    hour: '2-digit',
                                    minute: '2-digit',
                                    second: '2-digit',
                                    timeZone: 'Asia/Shanghai'
                                });
                                cell.value = formatter.format(dateValue);
                            }
                        });
                    });
                    var firstRow = worksheet.getRow(1);
                    firstRow.font = { name: 'Arial', size: 10, bold: true };  // 设置字体为 Arial，大小为 10，粗体
                    firstRow.alignment = { horizontal: 'center' };
                    // 导出为 Excel 文件
                    workbook.xlsx.writeBuffer().then(function (buffer) {
                        var blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
                        var link = document.createElement('a');
                        link.href = URL.createObjectURL(blob);
                        link.download = 'OrdersOverView.xlsx';
                        link.click();
                    });

                }
            }).catch(function (error) {
                console.log(error);
            }).finally(function () {
                vm.busying = false;
            });
        }
    }
]);