﻿
.btn-light-blue {
    background-color: #409CFF; /* 浅蓝色 */
    border-color: #409CFF; /* 边框颜色 */
    color: #fff; /* 文本颜色 */
}

.btn-light-blue:hover {
    background-color: #3075BF; /* 浅蓝色的稍微深一点的 hover 色 */
    border-color: #3094FF; /* 边框 hover 颜色 */
}

.btn-add {
    background-color: #28a745; /* 编辑按钮的绿色 */
    border-color: #28a745; /* 边框颜色 */
    color: #fff; /* 文本颜色 */
}

    .btn-add:hover {
        background-color: #218838; /* 悬停时的绿色稍微深一点 */
        border-color: #1e7e34; /* 边框悬停颜色 */
    }

.btn-edit {
    background-color: #d6a1f7; /* 柔和的紫色 */
    color: #fff;
    border: none;
}

    .btn-edit:hover {
        background-color: #b780f0; /* 稍微深一点的紫色 */
        color: #fff;
    }
#ag-header-cell {
    font-weight:bold;
}

.ag-header-cell-label {
    font-weight: bold !important;
}

.button-container-ag {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    gap: 5px; /* 按钮间距，可根据需要调整 */
}

    .button-container-ag button {
        margin: 0; /* 去除按钮的默认外边距 */
    }





.md-checkbox {
    position: relative;
    /* handling click events */
    /* when checkbox is checked */
}

    .md-checkbox.md-checkbox-inline {
        display: inline-block;
    }

.form-inline .md-checkbox.md-checkbox-inline {
    margin-right: 20px;
    top: 3px;
}

.md-checkbox input[type=checkbox] {
    visibility: hidden;
    position: absolute;
}

.md-checkbox label {
    cursor: pointer;
    padding-left: 30px;
}

    .md-checkbox label > span {
        display: block;
        position: absolute;
        left: 0;
        -webkit-transition-duration: 0.3s;
        -moz-transition-duration: 0.3s;
        transition-duration: 0.3s;
    }

        .md-checkbox label > span.inc {
            background: #fff;
            left: -20px;
            top: -20px;
            height: 60px;
            width: 60px;
            opacity: 0;
            border-radius: 50% !important;
            -moz-border-radius: 50% !important;
            -webkit-border-radius: 50% !important;
        }

    .md-checkbox label > .box {
        top: 0px;
        border: 2px solid #666;
        height: 20px;
        width: 20px;
        /*z-index: 5;*/
        -webkit-transition-delay: 0.2s;
        -moz-transition-delay: 0.2s;
        transition-delay: 0.2s;
    }

    .md-checkbox label > .check {
        top: -4px;
        left: 6px;
        width: 10px;
        height: 20px;
        border: 2px solid #36c6d3;
        border-top: none;
        border-left: none;
        opacity: 0;
       /* z-index: 5;*/
        -webkit-transform: rotate(180deg);
        -moz-transform: rotate(180deg);
        transform: rotate(180deg);
        -webkit-transition-delay: 0.3s;
        -moz-transition-delay: 0.3s;
        transition-delay: 0.3s;
    }

    .md-checkbox label > span.inc {
        -webkit-animation: growCircle 0.3s ease;
        -moz-animation: growCircle 0.3s ease;
        animation: growCircle 0.3s ease;
    }

.md-checkbox input[type=checkbox]:checked ~ label > .box {
    opacity: 0;
    -webkit-transform: scale(0) rotate(-180deg);
    -moz-transform: scale(0) rotate(-180deg);
    transform: scale(0) rotate(-180deg);
}

.md-checkbox input[type=checkbox]:checked ~ label > .check {
    opacity: 1;
    -webkit-transform: scale(1) rotate(45deg);
    -moz-transform: scale(1) rotate(45deg);
    transform: scale(1) rotate(45deg);
}

.md-checkbox input[type=checkbox]:disabled ~ label,
.md-checkbox input[type=checkbox][disabled] ~ label {
    cursor: not-allowed;
    opacity: 0.7;
    filter: alpha(opacity=70);
}

    .md-checkbox input[type=checkbox]:disabled ~ label > .box,
    .md-checkbox input[type=checkbox][disabled] ~ label > .box {
        cursor: not-allowed;
        opacity: 0.7;
        filter: alpha(opacity=70);
    }

.md-checkbox input[type=checkbox]:disabled:checked ~ label > .check,
.md-checkbox input[type=checkbox][disabled]:checked ~ label > .check {
    cursor: not-allowed;
    opacity: 0.7;
    filter: alpha(opacity=70);
}

.has-error .md-checkbox label,
.has-error.md-checkbox label {
    color: #e73d4a;
}

    .has-error .md-checkbox label > .box,
    .has-error.md-checkbox label > .box {
        border-color: #e73d4a;
    }

    .has-error .md-checkbox label > .check,
    .has-error.md-checkbox label > .check {
        border-color: #e73d4a;
    }

.has-success .md-checkbox label,
.has-success.md-checkbox label {
    color: #27a4b0;
}

    .has-success .md-checkbox label > .box,
    .has-success.md-checkbox label > .box {
        border-color: #27a4b0;
    }

    .has-success .md-checkbox label > .check,
    .has-success.md-checkbox label > .check {
        border-color: #27a4b0;
    }

.has-warning .md-checkbox label,
.has-warning.md-checkbox label {
    color: #c29d0b;
}

    .has-warning .md-checkbox label > .box,
    .has-warning.md-checkbox label > .box {
        border-color: #c29d0b;
    }

    .has-warning .md-checkbox label > .check,
    .has-warning.md-checkbox label > .check {
        border-color: #c29d0b;
    }

.has-info .md-checkbox label,
.has-info.md-checkbox label {
    color: #327ad5;
}

    .has-info .md-checkbox label > .box,
    .has-info.md-checkbox label > .box {
        border-color: #327ad5;
    }

    .has-info .md-checkbox label > .check,
    .has-info.md-checkbox label > .check {
        border-color: #327ad5;
    }

.form-md-checkboxes {
    padding-top: 5px;
}

    .form-md-checkboxes > label {
        font-size: 14px;
        color: #888888;
        opacity: 1;
        filter: alpha(opacity=100);
    }

    .form-md-checkboxes.has-error label {
        color: #ed6b75;
    }

    .form-md-checkboxes.has-info label {
        color: #659be0;
    }

    .form-md-checkboxes.has-success label {
        color: #36c6d3;
    }

    .form-md-checkboxes.has-warning label {
        color: #F1C40F;
    }

.md-checkbox-list {
    margin: 5px 0 5px 0;
}

.form-horizontal .md-checkbox-list {
    margin-top: 5px;
}

.md-checkbox-list .md-checkbox {
    display: block;
    margin-bottom: 10px;
}

    .md-checkbox-list .md-checkbox:last-child {
        margin-bottom: 0;
    }

.md-checkbox-inline {
    margin: 5px 0 5px 0;
    padding: 2% 0 0 15%;
}

.form-horizontal .md-checkbox-inline {
    margin-top: 7px;
}

.md-checkbox-inline .md-checkbox {
    display: inline-block;
    margin-right: 20px;
}

    .md-checkbox-inline .md-checkbox:last-child {
        margin-right: 0;
    }

.button-container-ag {
    display: flex;
    /*gap: 10px;*/ /* 设置按钮间的间距 */
    padding: 10px; /* 可选：为容器添加内边距 */
    /*border: 1px solid #ddd;*/ /* 可选：为容器添加边框以便更清晰地看到布局 */
    justify-content: flex-start;
}

/* 确保模态框内容的最大高度 */
.modal-body {
    max-height: 77vh; /* 根据需要调整 */
    height: auto;
    overflow-y: auto; /* 允许垂直滚动 auto visible */
}

/* 下拉框最大高度设置 */
.dropdown-menu {
    overflow-y: auto;  /*允许滚动 */
}

/* 选择项的样式调整 */
.inner {
    overflow-y: auto; /* 允许垂直滚动 */
}
.dropdown-menu {
    z-index: 5;
}

.new-row {
    /*//background-color: blanchedalmond;*/ /* 例如浅绿色 */
    background-color: lightgreen; /* 例如浅绿色 */
}

.custom-addon {
    display: inline-block; /* 使其成为行内块元素 */
    padding: 0 8px; /* 内边距 */
    color: #555; /* 文字颜色 */
    font-size: 14px; /* 字体大小 */
    vertical-align: middle; /* 垂直对齐 */
    cursor: default; /* 光标样式 */
    user-select: none; /* 禁止文本选择 */
}


.custom-tooltip {
    position: absolute;
    padding: 20px;
    width: 200px;
    pointer-events: none;
    transition: opacity 1s;
    overflow: auto;
    color: var(--ag-foreground-color);
    background-color: #5577cc;
}

    .custom-tooltip.ag-tooltip-hiding {
        opacity: 0;
    }

.my-custom-grid .ag-header.ag-focus-managed.ag-pivot-off.ag-header-allow-overflow {
    display: none !important;
}
/*.bootstrap-datetimepicker-widget {
    position: absolute;
    z-index: 1050 !important;*/ /* 提升 datetimepicker 的 z-index */
    /*overflow: visible;*/ /* 允许内容溢出 */
/*}*/


.fixed-navbar {
    z-index: 999; /* 确保固定导航栏不遮挡日期选择框 */
}

.datetimepicker-widget {
    z-index: 9999 !important; /* 确保日期选择框在所有元素上方 */
}
