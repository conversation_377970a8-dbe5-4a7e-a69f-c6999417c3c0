2025-04-22 08:56:53,436 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (53ms) [Parameters=[@__model_Account_0='Admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__model_Account_0
2025-04-22 08:56:53,540 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__userInfo_user_name_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_user_name_0
2025-04-22 08:56:53,798 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-22 08:56:53,800 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-22 08:56:53,814 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (3ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-22 08:56:56,451 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-22 08:56:56,455 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-22 08:56:59,772 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-22 08:56:59,783 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-22 08:57:02,929 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-22 08:57:02,929 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-22 09:03:17,349 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (64ms) [Parameters=[@__model_Account_0='Admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__model_Account_0
2025-04-22 09:03:17,464 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__userInfo_user_name_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_user_name_0
2025-04-22 09:03:17,552 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__model_Account_0='Admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__model_Account_0
2025-04-22 09:03:17,554 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__userInfo_user_name_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_user_name_0
2025-04-22 09:03:17,715 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-22 09:03:17,716 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-22 09:03:17,716 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-22 09:03:20,146 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-22 09:03:20,147 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-22 09:03:23,737 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-22 09:03:23,739 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-22 09:03:26,470 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-22 09:03:26,481 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-22 09:03:28,854 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-22 09:03:28,854 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-22 09:04:02,341 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (48ms) [Parameters=[@__model_Account_0='Admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__model_Account_0
2025-04-22 09:04:02,427 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__userInfo_user_name_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [s].[user_name], [s].[ept_restrictions], [s].[rank], [s].[role], [s].[role_restrictions]
FROM [sys_view_user_role] AS [s]
WHERE [s].[user_name] = @__userInfo_user_name_0
2025-04-22 09:04:02,673 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (2ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-22 09:04:02,677 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-22 09:04:02,679 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (1ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-22 09:04:04,552 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-22 09:04:04,554 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-22 09:04:07,559 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-22 09:04:07,574 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-22 09:04:09,269 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
2025-04-22 09:04:09,269 [.NET ThreadPool Worker] INFO  Microsoft.EntityFrameworkCore.Database.Command [(null)] - Executed DbCommand (0ms) [Parameters=[@__username_0='admin' (Size = 450)], CommandType='Text', CommandTimeout='30']
SELECT TOP(1) [u].[user_name], [u].[auser_status], [u].[department], [u].[department_id], [u].[department_old], [u].[dingding_user_id], [u].[email_address], [u].[name_pinyin], [u].[password], [u].[phone_number], [u].[real_name], [u].[register_time]
FROM [user_basic] AS [u]
WHERE [u].[user_name] = @__username_0
