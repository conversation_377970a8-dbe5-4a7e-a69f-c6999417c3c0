
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8"/>
    <title>IconFont</title>
    <link rel="stylesheet" href="demo.css">
    <link rel="stylesheet" href="iconfont.css">
</head>
<body>
    <div class="main markdown">
        <h1>IconFont 图标</h1>
        <ul class="icon_lists clear">
            
                <li>
                <i class="icon iconflow i-zhizhen1"></i>
                    <div class="name">指针</div>
                    <div class="fontclass">.i-zhizhen1</div>
                </li>
            
                <li>
                <i class="icon iconflow i-msnui-close-fat"></i>
                    <div class="name">关闭</div>
                    <div class="fontclass">.i-msnui-close-fat</div>
                </li>
            
                <li>
                <i class="icon iconflow i-close"></i>
                    <div class="name">close</div>
                    <div class="fontclass">.i-close</div>
                </li>
            
                <li>
                <i class="icon iconflow i-wenjiantianjia"></i>
                    <div class="name">文件添加</div>
                    <div class="fontclass">.i-wenjiantianjia</div>
                </li>
            
                <li>
                <i class="icon iconflow i-tag"></i>
                    <div class="name">tag</div>
                    <div class="fontclass">.i-tag</div>
                </li>
            
                <li>
                <i class="icon iconflow i-conowredo"></i>
                    <div class="name">conow-redo</div>
                    <div class="fontclass">.i-conowredo</div>
                </li>
            
                <li>
                <i class="icon iconflow i-conowrevoke"></i>
                    <div class="name">conow-revoke</div>
                    <div class="fontclass">.i-conowrevoke</div>
                </li>
            
                <li>
                <i class="icon iconflow i-baocun"></i>
                    <div class="name">保存</div>
                    <div class="fontclass">.i-baocun</div>
                </li>
            
                <li>
                <i class="icon iconflow i-refresh"></i>
                    <div class="name">Refresh</div>
                    <div class="fontclass">.i-refresh</div>
                </li>
            
                <li>
                <i class="icon iconflow i-youxiashixin"></i>
                    <div class="name">右下-实心</div>
                    <div class="fontclass">.i-youxiashixin</div>
                </li>
            
                <li>
                <i class="icon iconflow i-paizhaoanniu"></i>
                    <div class="name">拍照按钮</div>
                    <div class="fontclass">.i-paizhaoanniu</div>
                </li>
            
                <li>
                <i class="icon iconflow i-huizhang"></i>
                    <div class="name">徽章</div>
                    <div class="fontclass">.i-huizhang</div>
                </li>
            
                <li>
                <i class="icon iconflow i-hrgongzuotai"></i>
                    <div class="name">hr工作台</div>
                    <div class="fontclass">.i-hrgongzuotai</div>
                </li>
            
                <li>
                <i class="icon iconflow i-tingzhi-copy"></i>
                    <div class="name">停止</div>
                    <div class="fontclass">.i-tingzhi-copy</div>
                </li>
            
                <li>
                <i class="icon iconflow i-wenjianjia"></i>
                    <div class="name">文件夹</div>
                    <div class="fontclass">.i-wenjianjia</div>
                </li>
            
                <li>
                <i class="icon iconflow i-webtubiaoku08"></i>
                    <div class="name">声音</div>
                    <div class="fontclass">.i-webtubiaoku08</div>
                </li>
            
                <li>
                <i class="icon iconflow i-database"></i>
                    <div class="name">数据库</div>
                    <div class="fontclass">.i-database</div>
                </li>
            
                <li>
                <i class="icon iconflow i-chajian1"></i>
                    <div class="name">插件 (1)</div>
                    <div class="fontclass">.i-chajian1</div>
                </li>
            
                <li>
                <i class="icon iconflow i-caidan"></i>
                    <div class="name">菜单</div>
                    <div class="fontclass">.i-caidan</div>
                </li>
            
                <li>
                <i class="icon iconflow i-wangluo"></i>
                    <div class="name">网络</div>
                    <div class="fontclass">.i-wangluo</div>
                </li>
            
                <li>
                <i class="icon iconflow i-dayin"></i>
                    <div class="name">打印</div>
                    <div class="fontclass">.i-dayin</div>
                </li>
            
                <li>
                <i class="icon iconflow i-user"></i>
                    <div class="name">用户</div>
                    <div class="fontclass">.i-user</div>
                </li>
            
                <li>
                <i class="icon iconflow i-kaishi"></i>
                    <div class="name">开始</div>
                    <div class="fontclass">.i-kaishi</div>
                </li>
            
                <li>
                <i class="icon iconflow i-hebing"></i>
                    <div class="name">合并</div>
                    <div class="fontclass">.i-hebing</div>
                </li>
            
                <li>
                <i class="icon iconflow i-liaotian"></i>
                    <div class="name">聊天</div>
                    <div class="fontclass">.i-liaotian</div>
                </li>
            
                <li>
                <i class="icon iconflow i-dingshi"></i>
                    <div class="name">定时</div>
                    <div class="fontclass">.i-dingshi</div>
                </li>
            
                <li>
                <i class="icon iconflow i-xiejiantou"></i>
                    <div class="name">斜箭头</div>
                    <div class="fontclass">.i-xiejiantou</div>
                </li>
            
                <li>
                <i class="icon iconflow i-peizhi"></i>
                    <div class="name">配置</div>
                    <div class="fontclass">.i-peizhi</div>
                </li>
            
                <li>
                <i class="icon iconflow i-xiangmuzuhe"></i>
                    <div class="name">项目组合</div>
                    <div class="fontclass">.i-xiangmuzuhe</div>
                </li>
            
                <li>
                <i class="icon iconflow i-xitongcanshupeizhi"></i>
                    <div class="name">系统参数配置</div>
                    <div class="fontclass">.i-xitongcanshupeizhi</div>
                </li>
            
                <li>
                <i class="icon iconflow i-qukuai"></i>
                    <div class="name">区块</div>
                    <div class="fontclass">.i-qukuai</div>
                </li>
            
                <li>
                <i class="icon iconflow i-fenzhi"></i>
                    <div class="name">分支</div>
                    <div class="fontclass">.i-fenzhi</div>
                </li>
            
                <li>
                <i class="icon iconflow i-icon-test"></i>
                    <div class="name">01</div>
                    <div class="fontclass">.i-icon-test</div>
                </li>
            
                <li>
                <i class="icon iconflow i-icon-test1"></i>
                    <div class="name">02</div>
                    <div class="fontclass">.i-icon-test1</div>
                </li>
            
                <li>
                <i class="icon iconflow i-xiaoxuxian"></i>
                    <div class="name">小虚线</div>
                    <div class="fontclass">.i-xiaoxuxian</div>
                </li>
            
                <li>
                <i class="icon iconflow i-jianchagongjuzhixian"></i>
                    <div class="name">检查工具  直线</div>
                    <div class="fontclass">.i-jianchagongjuzhixian</div>
                </li>
            
        </ul>

        <h2 id="font-class-">font-class引用</h2>
        <hr>

        <p>font-class是unicode使用方式的一种变种，主要是解决unicode书写不直观，语意不明确的问题。</p>
        <p>与unicode使用方式相比，具有如下特点：</p>
        <ul>
        <li>兼容性良好，支持ie8+，及所有现代浏览器。</li>
        <li>相比于unicode语意明确，书写更直观。可以很容易分辨这个icon是什么。</li>
        <li>因为使用class来定义图标，所以当要替换图标时，只需要修改class里面的unicode引用。</li>
        <li>不过因为本质上还是使用的字体，所以多色图标还是不支持的。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的fontclass代码：</h3>


        <pre><code class="lang-js hljs javascript"><span class="hljs-comment">&lt;link rel="stylesheet" type="text/css" href="./iconfont.css"&gt;</span></code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
        <pre><code class="lang-css hljs">&lt;<span class="hljs-selector-tag">i</span> <span class="hljs-selector-tag">class</span>="<span class="hljs-selector-tag">iconflow</span> <span class="hljs-selector-tag">i-xxx</span>"&gt;&lt;/<span class="hljs-selector-tag">i</span>&gt;</code></pre>
        <blockquote>
        <p>"iconflow"是你项目下的font-family。可以通过编辑项目查看，默认是"iconfont"。</p>
        </blockquote>
    </div>
</body>
</html>
